/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import React from 'react';
import { Box } from 'ink';
import { MarkdownDisplay } from '../../utils/MarkdownDisplay.js';
import { Colors } from '../../colors.js';
import { AnimatedIcon } from '../shared/AnimatedIcon.js';

interface ArienMessageProps {
  text: string;
  isPending: boolean;
  availableTerminalHeight?: number;
  terminalWidth: number;
}

export const ArienMessage: React.FC<ArienMessageProps> = ({
  text,
  isPending,
  availableTerminalHeight,
  terminalWidth,
}) => {
  // Calculate available width for content, accounting for icon and margins
  // Icon takes 1 character + marginRight of 1 = 2 characters total
  const contentWidth = Math.max(20, terminalWidth - 2);
  
  return (
    <Box flexDirection="row">
      <Box marginRight={1}>
        <AnimatedIcon isPending={isPending} color={Colors.AccentPurple} />
      </Box>
      <Box flexGrow={1} flexDirection="column">
        <MarkdownDisplay
          text={text}
          isPending={isPending}
          availableTerminalHeight={availableTerminalHeight}
          terminalWidth={contentWidth}
        />
      </Box>
    </Box>
  );
};