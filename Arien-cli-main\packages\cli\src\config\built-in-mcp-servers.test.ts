/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { describe, it, expect } from 'vitest';
import {
  getBuiltInMcpServers,
  getConfigurableMcpServers,
  BUILT_IN_MCP_SERVERS,
  CONFIGURABLE_MCP_SERVERS,
} from './built-in-mcp-servers.js';
import { MCPServerConfig } from '@arien/arien-cli-core';

describe('Built-in MCP Servers', () => {
  describe('getBuiltInMcpServers', () => {
    it('should return a copy of built-in MCP servers', () => {
      const servers = getBuiltInMcpServers();
      
      // Should not be the same object reference
      expect(servers).not.toBe(BUILT_IN_MCP_SERVERS);
      
      // Should have the same content
      expect(servers).toEqual(BUILT_IN_MCP_SERVERS);
    });

    it('should include expected built-in servers', () => {
      const servers = getBuiltInMcpServers();
      
      const expectedServers = [
        'Context 7',
        'Playwright',
        'Sequential thinking',
        'Filesystem',
        'Fetch',
        'Memory',
        'Time',
        'Git',
        'Calculator',
        'QR Code',
        'DuckDuckGo Search',
        'Docker',
      ];
      
      expectedServers.forEach(serverName => {
        expect(servers).toHaveProperty(serverName);
        expect(servers[serverName]).toBeInstanceOf(MCPServerConfig);
      });
    });

    it('should have valid configurations for all built-in servers', () => {
      const servers = getBuiltInMcpServers();
      
      Object.entries(servers).forEach(([_name, config]) => {
        // Each server should have a command (for stdio transport)
        expect(config.command).toBeDefined();
        expect(typeof config.command).toBe('string');
        
        // Should have args array
        expect(config.args).toBeDefined();
        expect(Array.isArray(config.args)).toBe(true);
        
        // Should have a description
        expect(config.description).toBeDefined();
        expect(typeof config.description).toBe('string');
        
        // Should not have environment variables (these are basic servers)
        expect(config.env).toBeUndefined();
      });
    });
  });

  describe('getConfigurableMcpServers', () => {
    it('should return a copy of configurable MCP servers', () => {
      const servers = getConfigurableMcpServers();
      
      // Should not be the same object reference
      expect(servers).not.toBe(CONFIGURABLE_MCP_SERVERS);
      
      // Should have the same content
      expect(servers).toEqual(CONFIGURABLE_MCP_SERVERS);
    });

    it('should include expected configurable servers', () => {
      const servers = getConfigurableMcpServers();
      
      const expectedServers = [
        'Brave Search',
        'Web Scraping AI',
        'PostgreSQL',
        'SQLite',
        'MongoDB',
        'GitHub',
        'GitLab',
        'AWS',
        'Google Drive',
        'Notion',
        'Obsidian',
        'Linear',
        'Slack',
        'Discord',
        'OpenAI',
        'Perplexity',
        'VirusTotal',
        'Shodan',
        'Kubernetes',
        'Weather',
      ];
      
      expectedServers.forEach(serverName => {
        expect(servers).toHaveProperty(serverName);
        expect(servers[serverName]).toBeInstanceOf(MCPServerConfig);
      });
    });

    it('should have environment variables for servers that require them', () => {
      const servers = getConfigurableMcpServers();
      
      // Servers that should have environment variables
      const serversWithEnv = [
        'Brave Search',
        'Web Scraping AI',
        'PostgreSQL',
        'MongoDB',
        'GitHub',
        'GitLab',
        'AWS',
        'Google Drive',
        'Notion',
        'Linear',
        'Slack',
        'Discord',
        'OpenAI',
        'Perplexity',
        'VirusTotal',
        'Shodan',
        'Kubernetes',
        'Weather',
      ];
      
      serversWithEnv.forEach(serverName => {
        expect(servers[serverName].env).toBeDefined();
        expect(typeof servers[serverName].env).toBe('object');
        expect(Object.keys(servers[serverName].env!).length).toBeGreaterThan(0);
      });
    });

    it('should have placeholder values for API keys', () => {
      const servers = getConfigurableMcpServers();
      
      // Check that API key placeholders are present
      expect(servers['Brave Search'].env?.BRAVE_API_KEY).toBe('your_brave_api_key_here');
      expect(servers['GitHub'].env?.GITHUB_PERSONAL_ACCESS_TOKEN).toBe('your_github_token_here');
      expect(servers['OpenAI'].env?.OPENAI_API_KEY).toBe('your_openai_api_key');
    });
  });

  describe('Server Configuration Validation', () => {
    it('should have valid transport configuration for built-in servers', () => {
      const servers = getBuiltInMcpServers();

      Object.entries(servers).forEach(([_name, config]) => {
        // Should have at least one transport method
        const hasStdio = !!config.command;
        const hasSSE = !!config.url;
        const hasHTTP = !!config.httpUrl;
        const hasTCP = !!config.tcp;

        expect(hasStdio || hasSSE || hasHTTP || hasTCP).toBe(true);
      });
    });

    it('should have valid transport configuration for configurable servers', () => {
      const servers = getConfigurableMcpServers();

      Object.entries(servers).forEach(([_name, config]) => {
        // Should have at least one transport method
        const hasStdio = !!config.command;
        const hasSSE = !!config.url;
        const hasHTTP = !!config.httpUrl;
        const hasTCP = !!config.tcp;

        expect(hasStdio || hasSSE || hasHTTP || hasTCP).toBe(true);
      });
    });

    it('should use appropriate package managers', () => {
      const allServers = { ...getBuiltInMcpServers(), ...getConfigurableMcpServers() };
      
      Object.entries(allServers).forEach(([_name, config]) => {
        if (config.command) {
          // Should use either npx or uvx for package management
          expect(['npx', 'uvx'].includes(config.command)).toBe(true);
        }
      });
    });
  });

  describe('Immutability', () => {
    it('should not allow modification of returned built-in servers', () => {
      const servers1 = getBuiltInMcpServers();
      const servers2 = getBuiltInMcpServers();
      
      // Modify one copy
      delete servers1['Context 7'];
      
      // Other copy should be unaffected
      expect(servers2).toHaveProperty('Context 7');
    });

    it('should not allow modification of returned configurable servers', () => {
      const servers1 = getConfigurableMcpServers();
      const servers2 = getConfigurableMcpServers();
      
      // Modify one copy
      delete servers1['GitHub'];
      
      // Other copy should be unaffected
      expect(servers2).toHaveProperty('GitHub');
    });
  });
});
