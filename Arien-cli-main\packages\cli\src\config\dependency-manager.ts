/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { exec } from 'child_process';
import { promisify } from 'util';
import { platform } from 'os';

const execAsync = promisify(exec);

export interface DependencyInfo {
  name: string;
  command: string;
  version?: string;
  installed: boolean;
  installInstructions: string;
  autoInstallable: boolean;
}

export interface InstallResult {
  success: boolean;
  message: string;
  dependency: string;
}

/**
 * Manages dependencies required for MCP servers
 */
export class DependencyManager {
  private static readonly DEPENDENCIES: Record<string, DependencyInfo> = {
    node: {
      name: 'Node.js',
      command: 'node',
      installed: false,
      installInstructions: 'Install Node.js from https://nodejs.org/',
      autoInstallable: false,
    },
    npm: {
      name: 'npm',
      command: 'npm',
      installed: false,
      installInstructions: 'npm comes with Node.js. Install Node.js from https://nodejs.org/',
      autoInstallable: false,
    },
    npx: {
      name: 'npx',
      command: 'npx',
      installed: false,
      installInstructions: 'npx comes with Node.js. Install Node.js from https://nodejs.org/',
      autoInstallable: false,
    },
    python: {
      name: 'Python',
      command: 'python',
      installed: false,
      installInstructions: 'Install Python from https://python.org/',
      autoInstallable: false,
    },
    uv: {
      name: 'uv (Python package manager)',
      command: 'uv',
      installed: false,
      installInstructions: 'Install uv from https://docs.astral.sh/uv/getting-started/installation/',
      autoInstallable: true,
    },
    uvx: {
      name: 'uvx (Python package runner)',
      command: 'uvx',
      installed: false,
      installInstructions: 'uvx comes with uv. Install uv from https://docs.astral.sh/uv/getting-started/installation/',
      autoInstallable: true,
    },
    git: {
      name: 'Git',
      command: 'git',
      installed: false,
      installInstructions: 'Install Git from https://git-scm.com/',
      autoInstallable: false,
    },
  };

  /**
   * Checks if a command is available in the system
   */
  static async checkCommand(command: string): Promise<{ available: boolean; version?: string }> {
    try {
      const { stdout } = await execAsync(`${command} --version`, { timeout: 5000 });
      return {
        available: true,
        version: stdout.trim().split('\n')[0], // Get first line only
      };
    } catch (_error) {
      return { available: false };
    }
  }

  /**
   * Checks all dependencies and returns their status
   */
  static async checkAllDependencies(): Promise<Record<string, DependencyInfo>> {
    const results: Record<string, DependencyInfo> = {};

    for (const [key, dep] of Object.entries(this.DEPENDENCIES)) {
      const check = await this.checkCommand(dep.command);
      results[key] = {
        ...dep,
        installed: check.available,
        version: check.version,
      };
    }

    return results;
  }

  /**
   * Gets missing dependencies
   */
  static async getMissingDependencies(): Promise<DependencyInfo[]> {
    const allDeps = await this.checkAllDependencies();
    return Object.values(allDeps).filter(dep => !dep.installed);
  }

  /**
   * Gets auto-installable missing dependencies
   */
  static async getAutoInstallableDependencies(): Promise<DependencyInfo[]> {
    const missing = await this.getMissingDependencies();
    return missing.filter(dep => dep.autoInstallable);
  }

  /**
   * Attempts to auto-install uv (the main auto-installable dependency)
   */
  static async autoInstallUv(): Promise<InstallResult> {
    try {
      const currentPlatform = platform();
      let installCommand: string;

      switch (currentPlatform) {
        case 'win32':
          installCommand = 'powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"';
          break;
        case 'darwin':
        case 'linux':
          installCommand = 'curl -LsSf https://astral.sh/uv/install.sh | sh';
          break;
        default:
          return {
            success: false,
            message: `Auto-installation not supported on platform: ${currentPlatform}`,
            dependency: 'uv',
          };
      }

      console.log('Installing uv...');
      await execAsync(installCommand, { timeout: 120000 }); // 2 minutes timeout

      // Verify installation
      const uvCheck = await this.checkCommand('uv');
      const uvxCheck = await this.checkCommand('uvx');

      if (uvCheck.available && uvxCheck.available) {
        return {
          success: true,
          message: `Successfully installed uv ${uvCheck.version}. uvx is also available.`,
          dependency: 'uv',
        };
      } else {
        return {
          success: false,
          message: `Installation completed but uv/uvx not found in PATH. You may need to restart your terminal or add the installation directory to your PATH.`,
          dependency: 'uv',
        };
      }
    } catch (error) {
      return {
        success: false,
        message: `Failed to install uv: ${error}`,
        dependency: 'uv',
      };
    }
  }

  /**
   * Generates installation instructions for missing dependencies
   */
  static async generateInstallationInstructions(): Promise<string> {
    const missing = await this.getMissingDependencies();
    
    if (missing.length === 0) {
      return '✅ All required dependencies are installed!';
    }

    const autoInstallable = missing.filter(dep => dep.autoInstallable);
    const manualInstall = missing.filter(dep => !dep.autoInstallable);

    let instructions = '📋 Missing Dependencies:\n\n';

    if (autoInstallable.length > 0) {
      instructions += '🔧 Auto-installable:\n';
      autoInstallable.forEach(dep => {
        instructions += `  • ${dep.name} (${dep.command})\n`;
      });
      instructions += '\nRun the following to auto-install:\n';
      instructions += '  arien install-dependencies\n\n';
    }

    if (manualInstall.length > 0) {
      instructions += '📥 Manual installation required:\n';
      manualInstall.forEach(dep => {
        instructions += `  • ${dep.name} (${dep.command})\n`;
        instructions += `    ${dep.installInstructions}\n\n`;
      });
    }

    return instructions;
  }

  /**
   * Validates that required dependencies for specific MCP servers are available
   */
  static async validateMcpServerDependencies(serverName: string, command: string): Promise<{
    valid: boolean;
    missingDependencies: string[];
    instructions: string[];
  }> {
    const missing: string[] = [];
    const instructions: string[] = [];

    // Check the primary command
    const commandCheck = await this.checkCommand(command);
    if (!commandCheck.available) {
      missing.push(command);
      
      const depInfo = Object.values(this.DEPENDENCIES).find(dep => dep.command === command);
      if (depInfo) {
        instructions.push(depInfo.installInstructions);
      } else {
        instructions.push(`Install ${command} (check documentation for instructions)`);
      }
    }

    // Additional checks based on command type
    if (command === 'npx') {
      const nodeCheck = await this.checkCommand('node');
      if (!nodeCheck.available) {
        missing.push('node');
        instructions.push('Install Node.js from https://nodejs.org/');
      }
    }

    if (command === 'uvx') {
      const uvCheck = await this.checkCommand('uv');
      if (!uvCheck.available) {
        missing.push('uv');
        instructions.push('Install uv from https://docs.astral.sh/uv/getting-started/installation/');
      }
    }

    return {
      valid: missing.length === 0,
      missingDependencies: missing,
      instructions,
    };
  }

  /**
   * Checks system health for MCP server dependencies
   */
  static async getSystemHealthReport(): Promise<{
    healthy: boolean;
    summary: string;
    details: Record<string, DependencyInfo>;
    recommendations: string[];
  }> {
    const dependencies = await this.checkAllDependencies();
    const missing = Object.values(dependencies).filter(dep => !dep.installed);
    const installed = Object.values(dependencies).filter(dep => dep.installed);

    const recommendations: string[] = [];
    
    if (missing.length > 0) {
      const autoInstallable = missing.filter(dep => dep.autoInstallable);
      if (autoInstallable.length > 0) {
        recommendations.push('Run "arien install-dependencies" to auto-install missing dependencies');
      }
      
      const manual = missing.filter(dep => !dep.autoInstallable);
      if (manual.length > 0) {
        recommendations.push('Manually install missing dependencies (see details below)');
      }
    }

    return {
      healthy: missing.length === 0,
      summary: `${installed.length}/${Object.keys(dependencies).length} dependencies available`,
      details: dependencies,
      recommendations,
    };
  }
}
