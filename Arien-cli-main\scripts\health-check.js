#!/usr/bin/env node

/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

async function checkCommand(command) {
  try {
    const { stdout } = await execAsync(`${command} --version`, { timeout: 5000 });
    return {
      available: true,
      version: stdout.trim().split('\n')[0],
    };
  } catch (_error) {
    return { available: false };
  }
}

async function testMcpServer(name, command, args) {
  try {
    console.log(`  Testing ${name}...`);
    
    // For npm packages, try to get package info
    if (command === 'npx') {
      const packageName = args.find(arg => !arg.startsWith('-'));
      if (packageName) {
        try {
          const { stdout } = await execAsync(`npm view ${packageName} version`, { timeout: 10000 });
          if (stdout.trim()) {
            console.log(`    ✅ Package ${packageName} exists (version: ${stdout.trim()})`);
            return { success: true, message: `Package available` };
          }
        } catch (_error) {
          console.log(`    ❌ Package ${packageName} not found or network error`);
          return { success: false, message: `Package not found: ${packageName}` };
        }
      }
    }

    // For uvx packages, try to run with --help
    if (command === 'uvx') {
      const packageName = args[0];
      if (packageName) {
        try {
          const { stdout: _stdout, stderr: _stderr } = await execAsync(`uvx ${packageName} --help`, { timeout: 15000 });
          console.log(`    ✅ Package ${packageName} can be executed`);
          return { success: true, message: `Package executable` };
        } catch (_error) {
          console.log(`    ❌ Package ${packageName} failed to execute`);
          return { success: false, message: `Package execution failed: ${packageName}` };
        }
      }
    }

    return { success: true, message: 'Basic validation passed' };
  } catch (error) {
    return { success: false, message: error.message };
  }
}

async function healthCheck() {
  console.log('🏥 Arien MCP Server Health Check\n');

  // Check system dependencies
  console.log('1️⃣ System Dependencies:');
  const deps = {
    'Node.js': 'node',
    'npm': 'npm',
    'npx': 'npx',
    'uv': 'uv',
    'uvx': 'uvx',
    'Git': 'git',
  };

  let systemHealthy = true;
  for (const [name, command] of Object.entries(deps)) {
    const check = await checkCommand(command);
    if (check.available) {
      console.log(`  ✅ ${name}: ${check.version}`);
    } else {
      console.log(`  ❌ ${name}: Not available`);
      if (['node', 'npm', 'npx'].includes(command)) {
        systemHealthy = false; // Critical dependencies
      }
    }
  }

  console.log('\n2️⃣ MCP Server Packages:');
  
  // Test built-in MCP servers
  const mcpServers = [
    { name: 'Context 7', command: 'npx', args: ['-y', '@upstash/context7-mcp@latest'] },
    { name: 'Playwright', command: 'npx', args: ['-y', '@playwright/mcp@latest'] },
    { name: 'Sequential Thinking', command: 'npx', args: ['-y', '@modelcontextprotocol/server-sequential-thinking'] },
    { name: 'Filesystem', command: 'npx', args: ['-y', '@modelcontextprotocol/server-filesystem'] },
    { name: 'Memory', command: 'npx', args: ['-y', '@modelcontextprotocol/server-memory'] },
    { name: 'Git', command: 'uvx', args: ['mcp-server-git'] },
  ];

  let mcpHealthy = true;
  for (const server of mcpServers) {
    const commandCheck = await checkCommand(server.command);
    if (!commandCheck.available) {
      console.log(`  ⚠️  ${server.name}: Skipped (${server.command} not available)`);
      continue;
    }

    const result = await testMcpServer(server.name, server.command, server.args);
    if (result.success) {
      console.log(`  ✅ ${server.name}: ${result.message}`);
    } else {
      console.log(`  ❌ ${server.name}: ${result.message}`);
      mcpHealthy = false;
    }
  }

  console.log('\n3️⃣ Network Connectivity:');
  try {
    const { stdout: _stdout } = await execAsync('npm ping', { timeout: 10000 });
    console.log('  ✅ npm registry: Accessible');
  } catch (_error) {
    console.log('  ❌ npm registry: Connection failed');
    mcpHealthy = false;
  }

  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 Health Check Summary:');
  
  if (systemHealthy && mcpHealthy) {
    console.log('🎉 System is healthy! All MCP servers should work correctly.');
    console.log('\n💡 You can now use Arien with full MCP server support.');
  } else if (systemHealthy) {
    console.log('⚠️  System dependencies are OK, but some MCP servers may have issues.');
    console.log('\n💡 Some MCP servers may not work. Check the errors above.');
  } else {
    console.log('❌ Critical system dependencies are missing.');
    console.log('\n💡 Run "npm run install-deps" to install missing dependencies.');
  }

  console.log('\n🔧 Troubleshooting:');
  console.log('• Run "npm run check-deps" to see detailed dependency status');
  console.log('• Run "npm run install-deps" to auto-install missing dependencies');
  console.log('• Check your internet connection if package tests fail');
  console.log('• Restart your terminal after installing new dependencies');

  return systemHealthy && mcpHealthy;
}

async function main() {
  try {
    const healthy = await healthCheck();
    process.exit(healthy ? 0 : 1);
  } catch (error) {
    console.error('Error during health check:', error);
    process.exit(1);
  }
}

main();
