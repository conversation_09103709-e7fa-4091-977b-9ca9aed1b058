#!/usr/bin/env node

/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { exec } from 'child_process';
import { promisify } from 'util';
import { platform } from 'os';

const execAsync = promisify(exec);

async function checkCommand(command) {
  try {
    const { stdout } = await execAsync(`${command} --version`, { timeout: 5000 });
    return {
      available: true,
      version: stdout.trim().split('\n')[0],
    };
  } catch (_error) {
    return { available: false };
  }
}

async function installUv() {
  console.log('🔧 Installing uv (Python package manager)...');
  
  try {
    const currentPlatform = platform();
    let installCommand;

    switch (currentPlatform) {
      case 'win32':
        installCommand = 'powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"';
        break;
      case 'darwin':
      case 'linux':
        installCommand = 'curl -LsSf https://astral.sh/uv/install.sh | sh';
        break;
      default:
        console.log(`❌ Auto-installation not supported on platform: ${currentPlatform}`);
        console.log('Please install uv manually from: https://docs.astral.sh/uv/getting-started/installation/');
        return false;
    }

    console.log(`Running: ${installCommand}`);
    const { stdout: _stdout, stderr } = await execAsync(installCommand, { timeout: 120000 }); // 2 minutes timeout

    if (stderr) {
      console.log('Installation output:', stderr);
    }

    // Verify installation
    console.log('🔍 Verifying installation...');
    const uvCheck = await checkCommand('uv');
    const uvxCheck = await checkCommand('uvx');

    if (uvCheck.available && uvxCheck.available) {
      console.log(`✅ Successfully installed uv ${uvCheck.version}`);
      console.log(`✅ uvx is also available`);
      return true;
    } else {
      console.log('⚠️  Installation completed but uv/uvx not found in PATH.');
      console.log('You may need to:');
      console.log('1. Restart your terminal');
      console.log('2. Add the installation directory to your PATH');
      console.log('3. Source your shell configuration file');
      
      if (currentPlatform === 'win32') {
        console.log('\nFor Windows, the typical installation path is:');
        console.log('C:\\Users\\<USER>\\.local\\bin');
      } else {
        console.log('\nFor Unix systems, try running:');
        console.log('source ~/.bashrc  # or ~/.zshrc depending on your shell');
      }
      
      return false;
    }
  } catch (error) {
    console.error(`❌ Failed to install uv: ${error.message}`);
    console.log('\nPlease install uv manually from: https://docs.astral.sh/uv/getting-started/installation/');
    return false;
  }
}

async function checkAndInstallDependencies() {
  console.log('🚀 Arien MCP Server Dependency Installer\n');

  // Check current status
  const uvCheck = await checkCommand('uv');
  const uvxCheck = await checkCommand('uvx');
  const nodeCheck = await checkCommand('node');
  const npmCheck = await checkCommand('npm');
  const npxCheck = await checkCommand('npx');

  console.log('📊 Current Status:');
  console.log(`Node.js: ${nodeCheck.available ? '✅ ' + nodeCheck.version : '❌ Not found'}`);
  console.log(`npm: ${npmCheck.available ? '✅ ' + npmCheck.version : '❌ Not found'}`);
  console.log(`npx: ${npxCheck.available ? '✅ ' + npxCheck.version : '❌ Not found'}`);
  console.log(`uv: ${uvCheck.available ? '✅ ' + uvCheck.version : '❌ Not found'}`);
  console.log(`uvx: ${uvxCheck.available ? '✅ ' + uvxCheck.version : '❌ Not found'}`);

  console.log('\n' + '='.repeat(60));

  // Check Node.js dependencies
  if (!nodeCheck.available || !npmCheck.available || !npxCheck.available) {
    console.log('❌ Node.js ecosystem is not properly installed.');
    console.log('Please install Node.js from: https://nodejs.org/');
    console.log('This will provide node, npm, and npx commands.');
    return false;
  }

  // Install uv if missing
  if (!uvCheck.available || !uvxCheck.available) {
    console.log('📦 uv/uvx not found. Attempting to install...\n');
    const uvInstalled = await installUv();
    
    if (!uvInstalled) {
      console.log('\n⚠️  uv installation failed or incomplete.');
      console.log('Some MCP servers that require uvx will not work.');
      console.log('You can continue with npm-based MCP servers only.');
      return false;
    }
  } else {
    console.log('✅ uv and uvx are already installed.');
  }

  console.log('\n🎉 All auto-installable dependencies are ready!');
  console.log('\n💡 Next steps:');
  console.log('1. Restart your terminal if you just installed uv');
  console.log('2. Run "npm run check-deps" to verify all dependencies');
  console.log('3. Start using Arien with MCP servers!');

  return true;
}

async function main() {
  try {
    const success = await checkAndInstallDependencies();
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('Error during dependency installation:', error);
    process.exit(1);
  }
}

main();
